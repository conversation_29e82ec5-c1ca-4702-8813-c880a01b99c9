"use client"

import {
  Activity,
  ArrowUpRight,
  Book,
  Briefcase,
  CheckCircle,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  CircleUser,
  ClipboardList,
  CreditCard,
  DollarSign,
  Download,
  File,
  FileText,
  Home,
  Inbox,
  LayoutDashboard,
  LineChart,
  List,
  Menu,
  MoreVertical,
  Package,
  Package2,
  PanelLeft,
  PlusCircle,
  Search,
  Settings,
  ShoppingCart,
  Shield,
  Star,
  Users,
  Users2,
  Wallet,
  Zap,
  Bell
} from "lucide-react"

import { Badge } from "@/components/ui/badge"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
} from "@/components/ui/pagination"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import Link from "next/link"

export default function Dashboard() {
  return (
    <div className="flex-1 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-4xl font-light tracking-tight text-neutral-900 dark:text-white">
              Dashboard
            </h1>
            <p className="text-lg text-neutral-600 dark:text-neutral-400 font-light">
              Welcome back to your legal workspace
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="outline" size="icon" className="rounded-full bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50">
              <Bell className="h-5 w-5" />
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="rounded-full bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50"
                >
                  <CircleUser />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="bg-white/90 dark:bg-neutral-900/90 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50">
                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Settings</DropdownMenuItem>
                <DropdownMenuItem>Support</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Logout</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <main className="grid flex-1 items-start gap-8 lg:grid-cols-3 xl:grid-cols-3">
          <div className="grid auto-rows-max items-start gap-4 md:gap-8 lg:col-span-2">
            <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-4 lg:grid-cols-2 xl:grid-cols-4">
              <Card
                className="sm:col-span-2 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50" 
              >
                <CardHeader className="pb-3">
                  <CardTitle>Welcome to your Legal Dashboard</CardTitle>
                  <CardDescription className="max-w-lg text-balance leading-relaxed">
                    Analyze and manage your legal documents with ease.
                  </CardDescription>
                </CardHeader>
                <CardFooter>
                  <Button>Upload Document</Button>
                </CardFooter>
              </Card>
              <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50" >
                <CardHeader className="pb-2">
                  <CardDescription>Total Documents Scanned</CardDescription>
                  <CardTitle className="text-4xl">1,329</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    +25% from last week
                  </div>
                </CardContent>
                <CardFooter>
                  <Progress value={25} aria-label="25% increase" />
                </CardFooter>
              </Card>
              <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50" >
                <CardHeader className="pb-2">
                  <CardDescription>Time Saved</CardDescription>
                  <CardTitle className="text-4xl">5,329 hours</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-xs text-muted-foreground">
                    +10% from last month
                  </div>
                </CardContent>
                <CardFooter>
                  <Progress value={12} aria-label="12% increase" />
                </CardFooter>
              </Card>
            </div>
            <div>
              <Card className="bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50" >
                  <CardHeader className="px-7">
                    <CardTitle>Recent Documents</CardTitle>
                    <CardDescription>
                      Recent documents from your account.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Document</TableHead>
                          <TableHead className="hidden sm:table-cell">
                            Type
                          </TableHead>
                          <TableHead className="hidden sm:table-cell">
                            Status
                          </TableHead>
                          <TableHead className="hidden md:table-cell">
                            Date
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow className="bg-accent">
                          <TableCell>
                            <div className="font-medium">Contract Agreement</div>
                            <div className="hidden text-sm text-muted-foreground md:inline">
                              Project Alpha
                            </div>
                          </TableCell>
                          <TableCell className="hidden sm:table-cell">
                            Contract
                          </TableCell>
                          <TableCell className="hidden sm:table-cell">
                            <Badge className="text-xs" variant="secondary">
                              Reviewed
                            </Badge>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">
                            2023-06-23
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>
                            <div className="font-medium">Motion to Dismiss</div>
                            <div className="hidden text-sm text-muted-foreground md:inline">
                              Case No. 12345
                            </div>
                          </TableCell>
                          <TableCell className="hidden sm:table-cell">
                            Pleading
                          </TableCell>
                          <TableCell className="hidden sm:table-cell">
                            <Badge className="text-xs" variant="outline">
                              Drafting
                            </Badge>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">
                            2023-06-24
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>
                            <div className="font-medium">Discovery Request</div>
                            <div className="hidden text-sm text-muted-foreground md:inline">
                              Project Beta
                            </div>
                          </TableCell>
                          <TableCell className="hidden sm:table-cell">
                            Discovery
                          </TableCell>
                          <TableCell className="hidden sm:table-cell">
                            <Badge className="text-xs" variant="secondary">
                              Sent
                            </Badge>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">
                            2023-06-25
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>
                            <div className="font-medium">Affidavit of John Doe</div>
                            <div className="hidden text-sm text-muted-foreground md:inline">
                              Case No. 54321
                            </div>
                          </TableCell>
                          <TableCell className="hidden sm:table-cell">
                            Affidavit
                          </TableCell>
                          <TableCell className="hidden sm:table-cell">
                            <Badge className="text-xs" variant="secondary">
                              Signed
                            </Badge>
                          </TableCell>
                          <TableCell className="hidden md:table-cell">
                            2023-06-26
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>
            </div>
          </div>
          <div className="grid auto-rows-max items-start gap-4 md:gap-8 lg:col-span-1">
            <Card
              className="overflow-hidden bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50" 
            >
              <CardHeader className="flex flex-row items-start bg-muted/50">
                <div className="grid gap-0.5">
                  <CardTitle className="group flex items-center gap-2 text-lg">
                    Upcoming Tasks
                    <Button
                      size="icon"
                      variant="outline"
                      className="h-6 w-6 opacity-0 transition-opacity group-hover:opacity-100"
                    >
                      <PlusCircle className="h-4 w-4" />
                      <span className="sr-only">Add a new task</span>
                    </Button>
                  </CardTitle>
                  <CardDescription>Date: November 23, 2023</CardDescription>
                </div>
                <div className="ml-auto flex items-center gap-1">
                  <Button size="sm" variant="outline" className="h-8 gap-1">
                    <Download className="h-3.5 w-3.5" />
                    <span className="lg:sr-only xl:not-sr-only xl:whitespace-nowrap">
                      Export
                    </span>
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="p-6 text-sm">
                <div className="grid gap-4">
                  <div className="relative">
                    <div className="absolute left-3 top-3 h-full border-l-2 border-dashed border-muted" />
                    <div className="relative flex items-start gap-4">
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary">
                        <CheckCircle className="h-4 w-4 text-primary-foreground" />
                      </div>
                      <div className="grid gap-1">
                        <div className="font-semibold">Client Meeting</div>
                        <div className="text-xs text-muted-foreground">10:00 AM - 11:00 AM</div>
                        <div className="text-muted-foreground">Discussing case strategy for <em>Project Alpha</em></div>
                      </div>
                    </div>
                  </div>
                  <div className="relative">
                    <div className="absolute left-3 top-3 h-full border-l-2 border-dashed border-muted" />
                    <div className="relative flex items-start gap-4">
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary">
                        <FileText className="h-4 w-4 text-primary-foreground" />
                      </div>
                      <div className="grid gap-1">
                        <div className="font-semibold">Draft Motion</div>
                        <div className="text-xs text-muted-foreground">2:00 PM - 4:00 PM</div>
                        <div className="text-muted-foreground">Drafting motion for <em>Case No. 12345</em></div>
                      </div>
                    </div>
                  </div>
                  <div className="relative">
                    <div className="relative flex items-start gap-4">
                      <div className="flex h-6 w-6 items-center justify-center rounded-full bg-primary">
                        <Book className="h-4 w-4 text-primary-foreground" />
                      </div>
                      <div className="grid gap-1">
                        <div className="font-semibold">Legal Research</div>
                        <div className="text-xs text-muted-foreground">All Day</div>
                        <div className="text-muted-foreground">Researching precedents for <em>Project Beta</em></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
      </div>
  )
}
