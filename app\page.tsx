"use client"

"use client"

import { useEffect, useRef } from "react"
import { gsap } from "gsap"
import { ScrollTrigger } from "gsap/ScrollTrigger"
import { But<PERSON> } from "@/components/ui/button"
import { Scale, FileText, Brain, Zap, ArrowRight, CheckCircle, Shield, Clock } from "lucide-react"
import { motion } from "framer-motion"

gsap.registerPlugin(ScrollTrigger)

function FloatingPaths({ position }: { position: number }) {
  const paths = Array.from({ length: 36 }, (_, i) => ({
    id: i,
    d: `M${380 + i * 5 * position} -${189 + i * 6}C${
      380 + i * 5 * position
    } -${189 + i * 6} ${312 + i * 5 * position} ${216 - i * 6} ${
      -152 + i * 5 * position
    } ${343 - i * 6}C${-616 + i * 5 * position} ${470 - i * 6} ${
      -684 + i * 5 * position
    } ${875 - i * 6} ${-684 + i * 5 * position} ${875 - i * 6}`,
    color: `rgba(15,23,42,${0.1 + i * 0.03})`,
    width: 0.5 + i * 0.03,
  }))

  return (
    <div className="absolute inset-0 pointer-events-none">
      <svg className="w-full h-full text-slate-950 dark:text-white" viewBox="0 0 696 316" fill="none">
        <title>Background Paths</title>
        {paths.map((path) => (
          <motion.path
            key={path.id}
            d={path.d}
            stroke="currentColor"
            strokeWidth={path.width}
            strokeOpacity={0.05 + path.id * 0.01}
            initial={{ pathLength: 0.3, opacity: 0.3 }}
            animate={{
              pathLength: 1,
              opacity: [0.2, 0.4, 0.2],
              pathOffset: [0, 1, 0],
            }}
            transition={{
              duration: 25 + Math.random() * 15,
              repeat: Number.POSITIVE_INFINITY,
              ease: "linear",
            }}
          />
        ))}
      </svg>
    </div>
  )
}

function HeroSection() {
  const heroRef = useRef<HTMLDivElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const subtitleRef = useRef<HTMLParagraphElement>(null)
  const ctaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero animations
      gsap.fromTo(
        titleRef.current,
        { y: 100, opacity: 0 },
        { y: 0, opacity: 1, duration: 1.2, ease: "power3.out", delay: 0.3 },
      )

      gsap.fromTo(
        subtitleRef.current,
        { y: 50, opacity: 0 },
        { y: 0, opacity: 1, duration: 1, ease: "power3.out", delay: 0.6 },
      )

      gsap.fromTo(
        ctaRef.current,
        { y: 30, opacity: 0 },
        { y: 0, opacity: 1, duration: 0.8, ease: "power3.out", delay: 0.9 },
      )

      // Floating animation for the entire hero
      // gsap.to(heroRef.current, {
      //   y: -20,
      //   duration: 3,
      //   ease: "power1.inOut",
      //   yoyo: true,
      //   repeat: -1,
      // })
    }, heroRef)

    return () => ctx.revert()
  }, [])

  return (
    <section className="relative min-h-screen flex items-center justify-center bg-gradient-to-br from-white via-gray-50 to-white dark:from-neutral-950 dark:via-neutral-900 dark:to-neutral-950 pt-0">
      {/* Animated background paths */}
      <div className="absolute inset-0">
        <FloatingPaths position={1} />
        <FloatingPaths position={-1} />
      </div>

      {/* Minimal background pattern */}
      <div className="absolute inset-0 opacity-[0.02] dark:opacity-[0.05]">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, rgb(0,0,0) 1px, transparent 0)`,
            backgroundSize: "50px 50px",
          }}
        />
      </div>

      <div ref={heroRef} className="container mx-auto px-6 text-center relative z-10">
        <div className="max-w-5xl mx-auto">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full border border-neutral-200 dark:border-neutral-800 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-sm mb-8">
            <Scale className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
            <span className="text-sm font-medium text-neutral-600 dark:text-neutral-400">
              AI-Powered Legal Research
            </span>
          </div>

          <h1
            ref={titleRef}
            className="text-6xl md:text-8xl lg:text-9xl font-light tracking-tight mb-8 text-neutral-900 dark:text-white"
          >
            Legal
            <br />
            <span className="font-extralight text-neutral-600 dark:text-neutral-400">Intelligence</span>
          </h1>

          <p
            ref={subtitleRef}
            className="text-xl md:text-2xl text-neutral-600 dark:text-neutral-400 mb-12 max-w-3xl mx-auto font-light leading-relaxed"
          >
            Transform your legal practice with AI that researches, summarizes, and analyzes documents with unprecedented
            precision.
          </p>

          <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button
              size="lg"
              className="px-8 py-4 bg-neutral-900 hover:bg-neutral-800 dark:bg-white dark:hover:bg-neutral-100 text-white dark:text-neutral-900 rounded-full font-medium transition-all duration-300 hover:scale-105"
            >
              Start Free Trial
              <ArrowRight className="ml-2 w-4 h-4" />
            </Button>

            <Button
              variant="ghost"
              size="lg"
              className="px-8 py-4 text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white rounded-full font-medium"
            >
              Watch Demo
            </Button>
          </div>
        </div>
      </div>
    </section>
  )
}

function FeaturesSection() {
  const sectionRef = useRef<HTMLDivElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const featuresRef = useRef<HTMLDivElement>(null)

  const features = [
    {
      icon: Brain,
      title: "AI Research",
      description: "Scan millions of legal documents and precedents in seconds with advanced AI algorithms.",
      number: "01",
    },
    {
      icon: FileText,
      title: "Smart Summarization",
      description: "Get concise, accurate summaries with key insights and critical information highlighted.",
      number: "02",
    },
    {
      icon: Zap,
      title: "Instant Analysis",
      description: "Receive comprehensive reports with risk assessments and strategic recommendations.",
      number: "03",
    },
  ]

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Title animation
      gsap.fromTo(
        titleRef.current,
        { y: 50, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: titleRef.current,
            start: "top 80%",
          },
        },
      )

      // Features animation
      gsap.fromTo(
        ".feature-item",
        { y: 100, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 0.8,
          ease: "power3.out",
          stagger: 0.2,
          scrollTrigger: {
            trigger: featuresRef.current,
            start: "top 70%",
          },
        },
      )

      // Parallax effect for section
      gsap.to(sectionRef.current, {
        yPercent: -10,
        ease: "none",
        scrollTrigger: {
          trigger: sectionRef.current,
          start: "top bottom",
          end: "bottom top",
          scrub: true,
        },
      })
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section ref={sectionRef} className="py-32 px-6 bg-white dark:bg-neutral-950 relative overflow-hidden">
      {/* Minimal grid background */}
      <div className="absolute inset-0 opacity-[0.02] dark:opacity-[0.03]">
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)`,
            backgroundSize: "100px 100px",
          }}
        />
      </div>

      <div className="container mx-auto max-w-7xl relative z-10">
        <div className="text-center mb-24">
          <h2
            ref={titleRef}
            className="text-5xl md:text-7xl font-light tracking-tight mb-8 text-neutral-900 dark:text-white"
          >
            Capabilities
          </h2>
          <div className="w-24 h-px bg-neutral-300 dark:bg-neutral-700 mx-auto" />
        </div>

        <div ref={featuresRef} className="grid md:grid-cols-3 gap-16">
          {features.map((feature, index) => (
            <div key={index} className="feature-item group">
              <div className="relative">
                {/* Number */}
                <div className="text-8xl font-extralight text-neutral-200 dark:text-neutral-800 mb-6 leading-none">
                  {feature.number}
                </div>

                {/* Icon */}
                <div className="absolute top-0 right-0 p-4 rounded-full bg-neutral-100 dark:bg-neutral-900 group-hover:bg-neutral-900 dark:group-hover:bg-white transition-all duration-500">
                  <feature.icon className="w-6 h-6 text-neutral-600 dark:text-neutral-400 group-hover:text-white dark:group-hover:text-neutral-900 transition-colors duration-500" />
                </div>

                {/* Content */}
                <div className="mt-8">
                  <h3 className="text-2xl font-medium mb-4 text-neutral-900 dark:text-white">{feature.title}</h3>
                  <p className="text-neutral-600 dark:text-neutral-400 leading-relaxed font-light">
                    {feature.description}
                  </p>
                </div>

                {/* Hover line */}
                <div className="absolute bottom-0 left-0 w-0 h-px bg-neutral-900 dark:bg-white group-hover:w-full transition-all duration-500" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

function CTASection() {
  const sectionRef = useRef<HTMLDivElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const ctx = gsap.context(() => {
      gsap.fromTo(
        contentRef.current,
        { y: 50, opacity: 0 },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          ease: "power3.out",
          scrollTrigger: {
            trigger: contentRef.current,
            start: "top 80%",
          },
        },
      )
    }, sectionRef)

    return () => ctx.revert()
  }, [])

  return (
    <section ref={sectionRef} className="py-32 px-6 bg-neutral-50 dark:bg-neutral-900">
      <div className="container mx-auto max-w-4xl text-center">
        <div ref={contentRef}>
          <h2 className="text-5xl md:text-7xl font-light tracking-tight mb-8 text-neutral-900 dark:text-white">
            Ready to
            <br />
            <span className="font-extralight text-neutral-600 dark:text-neutral-400">Transform?</span>
          </h2>

          <p className="text-xl text-neutral-600 dark:text-neutral-400 mb-12 max-w-2xl mx-auto font-light leading-relaxed">
            Start your free trial today and experience the future of legal document analysis.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
            <Button
              size="lg"
              className="px-12 py-4 bg-neutral-900 hover:bg-neutral-800 dark:bg-white dark:hover:bg-neutral-100 text-white dark:text-neutral-900 rounded-full font-medium transition-all duration-300 hover:scale-105"
            >
              Get Started Free
              <ArrowRight className="ml-2 w-4 h-4" />
            </Button>

            <Button
              variant="ghost"
              size="lg"
              className="px-8 py-4 text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white rounded-full font-medium"
            >
              Schedule Demo
            </Button>
          </div>

          {/* Trust indicators */}
          <div className="flex items-center justify-center gap-12 text-sm text-neutral-500 dark:text-neutral-400">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-4 h-4" />
              <span>No credit card required</span>
            </div>
            <div className="flex items-center gap-2">
              <Shield className="w-4 h-4" />
              <span>Enterprise security</span>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>Setup in minutes</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default function HomePage() {
  return (
    <div className="min-h-screen">
      <HeroSection />
      <FeaturesSection />
      <CTASection />
    </div>
  )
}
