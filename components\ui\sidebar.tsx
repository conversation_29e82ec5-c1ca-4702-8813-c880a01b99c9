"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import {
  Home,
  Settings,
  FileText,
  Users,
  BarChart3,
  Scale,
  ChevronLeft,
  ChevronRight,
  Package2,
  Sparkles,
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

const sidebarItems = [
  { title: "Dashboard", href: "/dashboard", icon: Home },
  { title: "Documents", href: "/dashboard/documents", icon: FileText },
  { title: "Cases", href: "/dashboard/cases", icon: Scale },
  { title: "Analytics", href: "/dashboard/analytics", icon: BarChart3 },
  { title: "Team", href: "/dashboard/team", icon: Users },
  { title: "Settings", href: "/dashboard/settings", icon: Settings },
]

interface SidebarProps {
  className?: string
}

export function Sidebar({ className }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [hoveredItem, setHoveredItem] = useState<string | null>(null)
  const pathname = usePathname()

  useEffect(() => {
    setMounted(true)
  }, [])

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed)
  }

  if (!mounted) {
    return null
  }

  return (
    <motion.div
      initial={false}
      animate={{ width: isCollapsed ? 80 : 280 }}
      transition={{ duration: 0.4, ease: [0.4, 0, 0.2, 1] }}
      className={cn(
        "relative flex flex-col h-screen bg-white/60 dark:bg-neutral-900/60 backdrop-blur-2xl border-r border-neutral-200/30 dark:border-neutral-800/30 shadow-xl shadow-neutral-900/5 dark:shadow-neutral-900/20",
        className
      )}
    >
      {/* Subtle gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-white/20 to-transparent dark:from-neutral-800/20 pointer-events-none" />

      {/* Header */}
      <div className="relative flex items-center justify-between p-6 border-b border-neutral-200/30 dark:border-neutral-800/30">
        <AnimatePresence mode="wait">
          {!isCollapsed && (
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3, delay: 0.1, ease: [0.4, 0, 0.2, 1] }}
              className="flex items-center gap-3"
            >
              <motion.div
                className="relative p-2.5 rounded-xl bg-gradient-to-br from-neutral-100 to-neutral-50 dark:from-neutral-800 dark:to-neutral-900 border border-neutral-200/50 dark:border-neutral-700/50 shadow-sm"
                whileHover={{ scale: 1.05 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <Scale className="w-5 h-5 text-neutral-700 dark:text-neutral-200" />
                <motion.div
                  className="absolute inset-0 rounded-xl bg-gradient-to-br from-blue-500/10 to-purple-500/10 opacity-0"
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.2 }}
                />
              </motion.div>
              <div className="flex flex-col">
                <span className="text-lg font-light text-neutral-800 dark:text-white tracking-tight">
                  Legal<span className="font-extralight text-neutral-600 dark:text-neutral-300">AI</span>
                </span>
                <span className="text-xs text-neutral-500 dark:text-neutral-400 font-light">
                  Workspace
                </span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleSidebar}
            className="relative p-2 hover:bg-neutral-100/80 dark:hover:bg-neutral-800/80 rounded-xl transition-all duration-200 h-9 w-9 border border-transparent hover:border-neutral-200/50 dark:hover:border-neutral-700/50"
          >
            <motion.div
              animate={{ rotate: isCollapsed ? 180 : 0 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
            >
              {isCollapsed ? (
                <ChevronRight className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
              ) : (
                <ChevronLeft className="w-4 h-4 text-neutral-600 dark:text-neutral-400" />
              )}
            </motion.div>
          </Button>
        </motion.div>
      </div>

      {/* Navigation */}
      <nav className="relative flex-1 p-4 space-y-1">
        {sidebarItems.map((item, index) => {
          const isActive = pathname === item.href || pathname.startsWith(item.href + "/")

          return (
            <Link key={item.href} href={item.href} passHref>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1, duration: 0.3 }}
                onHoverStart={() => setHoveredItem(item.href)}
                onHoverEnd={() => setHoveredItem(null)}
                whileTap={{ scale: 0.98 }}
                className={cn(
                  "relative flex items-center gap-4 px-4 py-3.5 rounded-xl transition-all duration-300 group cursor-pointer overflow-hidden",
                  isActive
                    ? "text-neutral-900 dark:text-white bg-gradient-to-r from-neutral-100/80 to-neutral-50/80 dark:from-neutral-800/80 dark:to-neutral-900/80 border border-neutral-200/50 dark:border-neutral-700/50 shadow-sm"
                    : "text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white hover:bg-neutral-50/50 dark:hover:bg-neutral-800/30"
                )}
              >
                {/* Active indicator */}
                {isActive && (
                  <motion.div
                    layoutId="activeIndicator"
                    className="absolute left-0 w-1 h-8 bg-gradient-to-b from-neutral-700 to-neutral-900 dark:from-neutral-300 dark:to-white rounded-r-full"
                    transition={{ type: "spring", stiffness: 400, damping: 30 }}
                  />
                )}

                {/* Hover background effect */}
                <AnimatePresence>
                  {hoveredItem === item.href && !isActive && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      className="absolute inset-0 bg-gradient-to-r from-neutral-100/60 to-neutral-50/60 dark:from-neutral-800/40 dark:to-neutral-900/40 rounded-xl"
                      transition={{ duration: 0.2 }}
                    />
                  )}
                </AnimatePresence>

                {/* Icon with enhanced styling */}
                <motion.div
                  className="relative z-10 flex-shrink-0"
                  animate={{
                    scale: isActive ? 1.1 : 1,
                    rotate: hoveredItem === item.href ? 5 : 0,
                  }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <item.icon className={cn(
                    "w-5 h-5 transition-colors duration-200",
                    isActive
                      ? "text-neutral-800 dark:text-neutral-100"
                      : "text-neutral-500 dark:text-neutral-400 group-hover:text-neutral-700 dark:group-hover:text-neutral-200"
                  )} />
                </motion.div>

                {/* Text with improved animation */}
                <AnimatePresence mode="wait">
                  {!isCollapsed && (
                    <motion.span
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -10 }}
                      transition={{ duration: 0.3, delay: 0.1, ease: [0.4, 0, 0.2, 1] }}
                      className={cn(
                        "relative z-10 font-medium text-sm tracking-tight transition-colors duration-200",
                        isActive
                          ? "text-neutral-900 dark:text-white"
                          : "text-neutral-600 dark:text-neutral-400 group-hover:text-neutral-800 dark:group-hover:text-neutral-200"
                      )}
                    >
                      {item.title}
                    </motion.span>
                  )}
                </AnimatePresence>

                {/* Subtle shine effect on active */}
                {isActive && (
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent dark:via-white/5"
                    animate={{ x: [-100, 300] }}
                    transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                  />
                )}
              </motion.div>
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="relative p-4 border-t border-neutral-200/30 dark:border-neutral-800/30">
        <AnimatePresence mode="wait">
          {!isCollapsed ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.3, delay: 0.1, ease: [0.4, 0, 0.2, 1] }}
              className="relative overflow-hidden p-4 rounded-xl bg-gradient-to-br from-neutral-100/80 via-neutral-50/80 to-white/80 dark:from-neutral-800/80 dark:via-neutral-900/80 dark:to-neutral-950/80 border border-neutral-200/50 dark:border-neutral-700/50 shadow-lg backdrop-blur-sm"
            >
              {/* Animated background pattern */}
              <div className="absolute inset-0 opacity-5">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500" />
              </div>

              <div className="relative z-10">
                <div className="flex items-center gap-2 mb-2">
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  >
                    <Sparkles className="w-4 h-4 text-neutral-700 dark:text-neutral-300" />
                  </motion.div>
                  <div className="text-sm font-medium text-neutral-900 dark:text-white">
                    Upgrade to Pro
                  </div>
                </div>
                <div className="text-xs text-neutral-600 dark:text-neutral-400 mb-3 leading-relaxed">
                  Unlock advanced AI features and unlimited document processing
                </div>
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    size="sm"
                    className="w-full bg-gradient-to-r from-neutral-800 to-neutral-900 hover:from-neutral-700 hover:to-neutral-800 dark:from-white dark:to-neutral-100 dark:hover:from-neutral-100 dark:hover:to-neutral-200 text-white dark:text-neutral-900 shadow-lg transition-all duration-200 font-medium"
                  >
                    <Sparkles className="w-3 h-3 mr-2" />
                    Upgrade Now
                  </Button>
                </motion.div>
              </div>
            </motion.div>
          ) : (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
              className="flex justify-center"
            >
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                whileTap={{ scale: 0.9 }}
              >
                <Button
                  size="sm"
                  variant="ghost"
                  className="p-2.5 rounded-xl hover:bg-neutral-100/80 dark:hover:bg-neutral-800/80 border border-transparent hover:border-neutral-200/50 dark:hover:border-neutral-700/50 transition-all duration-200"
                >
                  <Sparkles className="w-5 h-5 text-neutral-600 dark:text-neutral-400" />
                </Button>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  )
}
