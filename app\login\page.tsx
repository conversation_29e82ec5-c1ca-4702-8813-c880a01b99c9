"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Mail, Lock, Eye, EyeOff, ArrowRight } from "lucide-react"

export default function LoginPage() {
  const [email, setEmail] = useState("<EMAIL>")
  const [password, setPassword] = useState("demopassword")
  const [showPassword, setShowPassword] = useState(false)
  const [isClient, setIsClient] = useState(false)
  const router = useRouter()
  
  const loginBoxRef = useRef<HTMLDivElement>(null)
  const titleRef = useRef<HTMLHeadingElement>(null)
  const formRef = useRef<HTMLFormElement>(null)

  // Fix hydration mismatch by only rendering client-specific content after mount
  useEffect(() => {
    setIsClient(true)
  }, [])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // In a real app, you would authenticate the user here
    // For demo, we'll just redirect to the home page
    router.push('/dashboard')
  }

  if (!isClient) {
    return null // Prevent rendering until client-side
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-white via-gray-50 to-white dark:from-neutral-950 dark:via-neutral-900 dark:to-neutral-950 p-6">
      <div 
        ref={loginBoxRef} 
        className="w-full max-w-md mx-auto relative z-10 bg-white/50 dark:bg-neutral-900/50 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-neutral-700/20 shadow-2xl shadow-black/10"
      >
        <div className="px-8 pt-8 pb-4 text-center">
          <h1 ref={titleRef} className="text-3xl font-light tracking-tight mb-2 text-neutral-900 dark:text-white">
            Welcome back
          </h1>
          <p className="text-neutral-600 dark:text-neutral-400 font-light mb-8">
            Sign in to continue to your account
          </p>
          
          <form ref={formRef} onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2 text-left">
              <Label htmlFor="email" className="text-sm font-light text-neutral-700 dark:text-neutral-300">
                Email
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-neutral-500 dark:text-neutral-400" />
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-10 bg-white/70 dark:bg-neutral-800/70 border-neutral-200 dark:border-neutral-700 focus:border-neutral-300 dark:focus:border-neutral-600"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
            
            <div className="space-y-2 text-left">
              <Label htmlFor="password" className="text-sm font-light text-neutral-700 dark:text-neutral-300">
                Password
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 w-5 h-5 text-neutral-500 dark:text-neutral-400" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10 pr-10 bg-white/70 dark:bg-neutral-800/70 border-neutral-200 dark:border-neutral-700 focus:border-neutral-300 dark:focus:border-neutral-600"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-neutral-500 dark:text-neutral-400 hover:text-neutral-700 dark:hover:text-neutral-200"
                >
                  {showPassword ? 
                    <EyeOff className="w-5 h-5" /> : 
                    <Eye className="w-5 h-5" />
                  }
                </button>
              </div>
            </div>
            
            <Button 
              type="submit" 
              className="w-full px-8 py-6 h-auto bg-neutral-900 hover:bg-neutral-800 dark:bg-white dark:hover:bg-neutral-100 text-white dark:text-neutral-900 rounded-xl font-medium transition-all duration-300 hover:scale-[1.02] text-base"
            >
              Sign In
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </form>
          
          <div className="mt-8 pt-6 border-t border-neutral-200 dark:border-neutral-800">
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              Demo credentials have been pre-filled for you
            </p>
          </div>
        </div>
      </div>
    </div>
  )
} 