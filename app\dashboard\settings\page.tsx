"use client"

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { toast } from "@/hooks/use-toast"
import { useTheme } from "next-themes"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Palette, 
  Bell, 
  User, 
  Shield, 
  Globe, 
  Moon, 
  Sun, 
  Monitor,
  Mail,
  Smartphone,
  Settings as SettingsIcon,
  UserCircle
} from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"

// Schemas
const appearanceFormSchema = z.object({
  theme: z.enum(["light", "dark", "system"], {
    required_error: "Please select a theme.",
  }),
})

const profileFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters.").max(50, "Name must not exceed 50 characters."),
  email: z.string().email("Please enter a valid email address."),
  bio: z.string().max(160, "Bio must not exceed 160 characters.").optional(),
  language: z.string({ required_error: "Please select a language." }),
})

const notificationFormSchema = z.object({
  marketing: z.boolean().default(false),
  security: z.boolean().default(true),
  updates: z.boolean().default(true),
  mobile: z.boolean().default(false),
  email: z.boolean().default(true),
})

const securityFormSchema = z.object({
  twoFactor: z.boolean().default(false),
  loginAlerts: z.boolean().default(true),
  sessionTimeout: z.string().default("30"),
})

// Form Values Types
type AppearanceFormValues = z.infer<typeof appearanceFormSchema>
type ProfileFormValues = z.infer<typeof profileFormSchema>
type NotificationFormValues = z.infer<typeof notificationFormSchema>
type SecurityFormValues = z.infer<typeof securityFormSchema>

// Theme Preview Component with proper typing
const ThemePreview = ({ theme }: { theme: "light" | "dark" | "system" }) => {
  const getIcon = () => {
    switch (theme) {
      case "light": return <Sun className="w-4 h-4" />
      case "dark": return <Moon className="w-4 h-4" />
      default: return <Monitor className="w-4 h-4" />
    }
  }

  if (theme === "system") {
    return (
      <div className="relative">
        <div className="w-full h-20 rounded-lg border-2 border-muted bg-background flex overflow-hidden shadow-sm">
          <div className="w-1/2 h-full bg-white flex items-center justify-center">
            <div className="w-8 h-8 rounded bg-neutral-200" />
          </div>
          <div className="w-1/2 h-full bg-neutral-950 flex items-center justify-center">
            <div className="w-8 h-8 rounded bg-neutral-700" />
          </div>
        </div>
        <div className="absolute top-2 right-2 p-1 rounded-full bg-background/80 backdrop-blur-sm">
          {getIcon()}
        </div>
      </div>
    )
  }
  
  return (
    <div className="relative">
      <div className={cn(
        "w-full h-20 rounded-lg border-2 shadow-sm transition-all duration-200", 
        theme === "light" 
          ? "bg-white border-neutral-200" 
          : "bg-neutral-950 border-neutral-800"
      )}>
        <div className="p-3 space-y-2">
          <div className={cn(
            "w-3/4 h-3 rounded-full", 
            theme === "light" ? "bg-neutral-300" : "bg-neutral-700"
          )} />
          <div className={cn(
            "w-1/2 h-3 rounded-full", 
            theme === "light" ? "bg-neutral-200" : "bg-neutral-800"
          )} />
          <div className={cn(
            "w-2/3 h-3 rounded-full", 
            theme === "light" ? "bg-neutral-100" : "bg-neutral-800"
          )} />
        </div>
      </div>
      <div className="absolute top-2 right-2 p-1.5 rounded-full bg-background/80 backdrop-blur-sm">
        {getIcon()}
      </div>
    </div>
  )
}

export default function SettingsPage() {
  const { setTheme, theme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const [activeCard, setActiveCard] = useState<string | null>(null)

  useEffect(() => {
    setMounted(true)
  }, [])

  const appearanceForm = useForm<AppearanceFormValues>({
    resolver: zodResolver(appearanceFormSchema),
    defaultValues: {
      theme: (theme as "light" | "dark" | "system") || "system",
    },
  })

  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: "John Doe",
      email: "<EMAIL>",
      bio: "Legal professional specializing in corporate law.",
      language: "en",
    },
  })

  const notificationForm = useForm<NotificationFormValues>({
    resolver: zodResolver(notificationFormSchema),
    defaultValues: {
      marketing: false,
      security: true,
      updates: true,
      mobile: false,
      email: true,
    },
  })

  const securityForm = useForm<SecurityFormValues>({
    resolver: zodResolver(securityFormSchema),
    defaultValues: {
      twoFactor: false,
      loginAlerts: true,
      sessionTimeout: "30",
    },
  })

  function onAppearanceSubmit(data: AppearanceFormValues) {
    setTheme(data.theme)
    toast({
      title: "Theme Updated",
      description: `Switched to ${data.theme} theme.`,
    })
  }

  function onProfileSubmit(data: ProfileFormValues) {
    console.log(data)
    toast({
      title: "Profile Updated",
      description: "Your profile information has been saved.",
    })
  }

  function onNotificationSubmit(data: NotificationFormValues) {
    console.log(data)
    toast({
      title: "Notifications Updated",
      description: "Your notification settings have been saved.",
    })
  }

  function onSecuritySubmit(data: SecurityFormValues) {
    console.log(data)
    toast({
      title: "Security Settings Updated",
      description: "Your security preferences have been saved.",
    })
  }

  if (!mounted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-neutral-100 dark:from-neutral-950 dark:to-neutral-900">
        <div className="flex h-screen">
          {/* Sidebar Skeleton */}
          <div className="w-64 bg-white/80 dark:bg-neutral-900/80 backdrop-blur-xl border-r border-neutral-200/50 dark:border-neutral-800/50">
            <div className="p-6 space-y-4">
              <div className="h-8 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse" />
              <div className="space-y-2">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-6 bg-neutral-100 dark:bg-neutral-800/50 rounded animate-pulse" />
                ))}
              </div>
            </div>
          </div>
          {/* Content Skeleton */}
          <div className="flex-1 p-8">
            <div className="max-w-6xl mx-auto space-y-8">
              <div className="space-y-2">
                <div className="h-10 w-48 bg-neutral-200 dark:bg-neutral-800 rounded animate-pulse" />
                <div className="h-6 w-96 bg-neutral-100 dark:bg-neutral-800/50 rounded animate-pulse" />
              </div>
              <div className="grid grid-cols-12 gap-6">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="col-span-6 h-64 bg-neutral-100 dark:bg-neutral-800/30 rounded-xl animate-pulse" />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }
  
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const cardVariants = {
    hidden: { 
      opacity: 0, 
      y: 30,
      scale: 0.95,
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  }

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94],
      },
    },
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-white to-neutral-100 dark:from-neutral-950 dark:via-neutral-900 dark:to-neutral-950">
      <div className="flex h-screen">
        {/* Static Sidebar */}
        <motion.div 
          initial={{ x: -100, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="fixed left-0 top-0 z-30 w-64 h-full bg-white/80 dark:bg-neutral-900/80 backdrop-blur-xl border-r border-neutral-200/50 dark:border-neutral-800/50"
        >
          <div className="p-6">
            <motion.div 
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.5 }}
              className="flex items-center gap-3 mb-8"
            >
              <div className="p-2 rounded-xl bg-gradient-to-br from-neutral-100 to-neutral-200 dark:from-neutral-800 dark:to-neutral-700">
                <SettingsIcon className="w-6 h-6 text-neutral-700 dark:text-neutral-200" />
              </div>
              <h2 className="text-xl font-semibold text-neutral-900 dark:text-white">Settings</h2>
            </motion.div>
            
            <nav className="space-y-2">
              {[
                { icon: UserCircle, label: "Profile", id: "profile" },
                { icon: Palette, label: "Appearance", id: "appearance" },
                { icon: Bell, label: "Notifications", id: "notifications" },
                { icon: Shield, label: "Security", id: "security" },
              ].map((item, index) => (
                <motion.button
                  key={item.id}
                  initial={{ x: -20, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  transition={{ delay: 0.4 + index * 0.1, duration: 0.4 }}
                  onClick={() => {
                    const element = document.getElementById(item.id)
                    element?.scrollIntoView({ behavior: 'smooth', block: 'start' })
                  }}
                  className="flex items-center gap-3 w-full p-3 rounded-lg text-left transition-all duration-200 hover:bg-neutral-100 dark:hover:bg-neutral-800/50 group"
                >
                  <item.icon className="w-5 h-5 text-neutral-600 dark:text-neutral-400 group-hover:text-neutral-900 dark:group-hover:text-white transition-colors" />
                  <span className="text-sm font-medium text-neutral-700 dark:text-neutral-300 group-hover:text-neutral-900 dark:group-hover:text-white transition-colors">
                    {item.label}
                  </span>
                </motion.button>
              ))}
            </nav>
          </div>
        </motion.div>

        {/* Main Content */}
        <div className="flex-1 ml-64 overflow-y-auto">
          <div className="p-8">
            <div className="max-w-6xl mx-auto">
              <motion.div
                variants={headerVariants}
                initial="hidden"
                animate="visible"
                className="mb-12"
              >
                <h1 className="text-4xl md:text-5xl font-light tracking-tight text-neutral-900 dark:text-white mb-3">
                  Settings
                </h1>
                <p className="text-lg text-neutral-600 dark:text-neutral-400 font-light">
                  Manage your account preferences and customize your experience.
                </p>
              </motion.div>

              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="grid grid-cols-12 gap-6 auto-rows-fr"
              >
                {/* Profile Card - Large */}
                <motion.div
                  id="profile"
                  variants={cardVariants}
                  onHoverStart={() => setActiveCard("profile")}
                  onHoverEnd={() => setActiveCard(null)}
                  className="col-span-12 lg:col-span-8"
                >
                  <Card className={cn(
                    "h-full bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50 transition-all duration-300",
                    activeCard === "profile" && "shadow-2xl shadow-neutral-200/50 dark:shadow-neutral-900/50 scale-[1.02]"
                  )}>
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-4">
                        <motion.div 
                          animate={{ 
                            scale: activeCard === "profile" ? 1.1 : 1,
                            rotate: activeCard === "profile" ? 5 : 0 
                          }}
                          className="p-3 rounded-xl bg-gradient-to-br from-blue-100 to-blue-50 dark:from-blue-900/30 dark:to-blue-800/30"
                        >
                          <User className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                        </motion.div>
                        <div>
                          <CardTitle className="text-2xl font-semibold">Profile Information</CardTitle>
                          <CardDescription className="text-base">
                            Update your personal details and preferences.
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Form {...profileForm}>
                        <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-6">
                          <div className="grid md:grid-cols-2 gap-6">
                            <FormField
                              control={profileForm.control}
                              name="name"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-sm font-medium">Full Name</FormLabel>
                                  <FormControl>
                                    <Input 
                                      {...field} 
                                      placeholder="John Doe" 
                                      className="h-11 bg-white/50 dark:bg-neutral-800/50 border-neutral-300 dark:border-neutral-600 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={profileForm.control}
                              name="email"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-sm font-medium">Email Address</FormLabel>
                                  <FormControl>
                                    <Input 
                                      {...field} 
                                      type="email" 
                                      placeholder="<EMAIL>" 
                                      className="h-11 bg-white/50 dark:bg-neutral-800/50 border-neutral-300 dark:border-neutral-600 focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <FormField
                            control={profileForm.control}
                            name="bio"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm font-medium">Bio</FormLabel>
                                <FormControl>
                                  <Textarea 
                                    {...field} 
                                    placeholder="Tell us about yourself..." 
                                    className="min-h-[100px] bg-white/50 dark:bg-neutral-800/50 border-neutral-300 dark:border-neutral-600 focus:border-blue-500 dark:focus:border-blue-400 transition-colors resize-none"
                                  />
                                </FormControl>
                                <FormDescription>
                                  {field.value?.length || 0}/160 characters
                                </FormDescription>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={profileForm.control}
                            name="language"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm font-medium">Language</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger className="h-11 bg-white/50 dark:bg-neutral-800/50 border-neutral-300 dark:border-neutral-600">
                                      <SelectValue placeholder="Select a language" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="en">🇺🇸 English</SelectItem>
                                    <SelectItem value="es">🇪🇸 Spanish</SelectItem>
                                    <SelectItem value="fr">🇫🇷 French</SelectItem>
                                    <SelectItem value="de">🇩🇪 German</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <motion.div
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <Button 
                              type="submit" 
                              size="lg"
                              className="bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                            >
                              Save Changes
                            </Button>
                          </motion.div>
                        </form>
                      </Form>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Appearance Card - Medium */}
                <motion.div
                  id="appearance"
                  variants={cardVariants}
                  onHoverStart={() => setActiveCard("appearance")}
                  onHoverEnd={() => setActiveCard(null)}
                  className="col-span-12 lg:col-span-4"
                >
                  <Card className={cn(
                    "h-full bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50 transition-all duration-300",
                    activeCard === "appearance" && "shadow-2xl shadow-neutral-200/50 dark:shadow-neutral-900/50 scale-[1.02]"
                  )}>
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-3">
                        <motion.div 
                          animate={{ 
                            scale: activeCard === "appearance" ? 1.1 : 1,
                            rotate: activeCard === "appearance" ? -5 : 0 
                          }}
                          className="p-3 rounded-xl bg-gradient-to-br from-purple-100 to-purple-50 dark:from-purple-900/30 dark:to-purple-800/30"
                        >
                          <Palette className="w-6 h-6 text-purple-600 dark:text-purple-400" />
                        </motion.div>
                        <div>
                          <CardTitle className="text-xl font-semibold">Theme</CardTitle>
                          <CardDescription>
                            Customize your visual experience.
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Form {...appearanceForm}>
                        <form onSubmit={appearanceForm.handleSubmit(onAppearanceSubmit)} className="space-y-6">
                          <FormField
                            control={appearanceForm.control}
                            name="theme"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-sm font-medium">Choose Theme</FormLabel>
                                <FormControl>
                                  <RadioGroup
                                    onValueChange={field.onChange}
                                    value={field.value}
                                    className="space-y-3"
                                  >
                                    {(["light", "dark", "system"] as const).map((themeValue) => (
                                      <FormItem key={themeValue}>
                                        <FormLabel className="cursor-pointer relative block">
                                          <FormControl>
                                            <RadioGroupItem value={themeValue} className="sr-only" />
                                          </FormControl>
                                          <motion.div
                                            whileHover={{ scale: 1.02 }}
                                            whileTap={{ scale: 0.98 }}
                                            className="relative"
                                          >
                                            <ThemePreview theme={themeValue} />
                                            <div className="mt-3 text-center">
                                              <div className="text-sm font-medium capitalize text-neutral-900 dark:text-white">
                                                {themeValue}
                                              </div>
                                            </div>
                                            <AnimatePresence>
                                              {field.value === themeValue && (
                                                <motion.div
                                                  initial={{ scale: 0.8, opacity: 0 }}
                                                  animate={{ scale: 1, opacity: 1 }}
                                                  exit={{ scale: 0.8, opacity: 0 }}
                                                  className="absolute inset-0 rounded-lg border-2 border-purple-500 ring-4 ring-purple-500/20 bg-purple-500/5"
                                                />
                                              )}
                                            </AnimatePresence>
                                          </motion.div>
                                        </FormLabel>
                                      </FormItem>
                                    ))}
                                  </RadioGroup>
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <motion.div
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <Button 
                              type="submit" 
                              className="w-full bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                            >
                              Apply Theme
                            </Button>
                          </motion.div>
                        </form>
                      </Form>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Security Card - Medium */}
                <motion.div
                  id="security"
                  variants={cardVariants}
                  onHoverStart={() => setActiveCard("security")}
                  onHoverEnd={() => setActiveCard(null)}
                  className="col-span-12 lg:col-span-6"
                >
                  <Card className={cn(
                    "h-full bg-gradient-to-br from-white/80 to-white/40 dark:from-neutral-900/80 dark:to-neutral-900/40 backdrop-blur-xl border-neutral-200/50 dark:border-neutral-800/50 transition-all duration-300",
                    activeCard === "security" && "shadow-2xl shadow-neutral-200/50 dark:shadow-neutral-900/50 scale-[1.02]"
                  )}>
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-3">
                        <motion.div 
                          animate={{ 
                            scale: activeCard === "security" ? 1.1 : 1,
                            rotate: activeCard === "security" ? 5 : 0 
                          }}
                          className="p-3 rounded-xl bg-gradient-to-br from-red-100 to-red-50 dark:from-red-900/30 dark:to-red-800/30"
                        >
                          <Shield className="w-6 h-6 text-red-600 dark:text-red-400" />
                        </motion.div>
                        <div>
                          <CardTitle className="text-xl font-semibold">Security</CardTitle>
                          <CardDescription>
                            Manage your account security settings.
                          </CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <Form {...securityForm}>
                        <form onSubmit={securityForm.handleSubmit(onSecuritySubmit)} className="space-y-6">
                          <div className="space-y-4">
                            <FormField
                              control={securityForm.control}
                              name="twoFactor"
                              render={({ field }) => (
                                <FormItem className="flex items-center justify-between p-4 rounded-xl bg-white/60 dark:bg-neutral-800/30 border border-neutral-200/50 dark:border-neutral-700/50">
                                  <div>
                                    <FormLabel className="font-medium">Two-Factor Authentication</FormLabel>
                                    <FormDescription className="text-sm">
                                      Add an extra layer of security to your account.
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={securityForm.control}
                              name="loginAlerts"
                              render={({ field }) => (
                                <FormItem className="flex items-center justify-between p-4 rounded-xl bg-white/60 dark:bg-neutral-800/30 border border-neutral-200/50 dark:border-neutral-700/50">
                                  <div>
                                    <FormLabel className="font-medium">Login Alerts</FormLabel>
                                    <FormDescription className="text-sm">
                                      Get notified of new login attempts.
                                    </FormDescription>
                                  </div>
                                  <FormControl>
                                    <Switch
                                      checked={field.value}
                                      onCheckedChange={field.onChange}
                                    />
                                  </FormControl>
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={securityForm.control}
                              name="sessionTimeout"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="font-medium">Session Timeout (minutes)</FormLabel>
                                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                      <SelectTrigger className="bg-white/60 dark:bg-neutral-800/30 border-neutral-200/50 dark:border-neutral-700/50">
                                        <SelectValue />
                                      </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                      <SelectItem value="15">15 minutes</SelectItem>
                                      <SelectItem value="30">30 minutes</SelectItem>
                                      <SelectItem value="60">1 hour</SelectItem>
                                      <SelectItem value="240">4 hours</SelectItem>
                                    </SelectContent>
                                  </Select>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                          <motion.div
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <Button 
                              type="submit" 
                              className="w-full bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                            >
                              Apply Security Settings
                            </Button>
                          </motion.div>
                        </form>
                      </Form>
                    </CardContent>
                  </Card>
                </motion.div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SettingsPage
  